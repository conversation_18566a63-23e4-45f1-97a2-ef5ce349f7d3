#!/bin/bash

# Path to your Python script
PYTHON_SCRIPT="./train_sg_imp_score_simul_v4_energy_di.py"
PYTHON_SCRIPT_RENDER="./render_spherical_gaussian_cullSG_v3.py"
PYTHON_SCRIPT_METRICS="./metrics.py"
PYTHON_SCRIPT_COUNT="./count_spherical_gaussians_v2.py"
PYTHON_SCRIPT_MEM="./render_spherical_gaussian_mem_test_v2_light.py"
# BASE_DATASET_DIR="../Feature_Gaussian_Compression/mip360"
BASE_DATASET_DIR="../dataset/tandt"
# BASE_DATASET_DIR="../dataset/db"
chkpnt_iter=14999

# Results log file
RESULTS_LOG="training_results.txt"
declare -a run_scenes=(
  # "bicycle"
  # "bonsai"
  # "counter"
  # "kitchen"
  # "room"
  # "stump"
  # "garden"
  "train"
  # "truck"
  # "treehill"
  # "playroom"
  # "dr<PERSON><PERSON><PERSON>"
  # "flowers"
)


PORT="1242"
SPA_INTERVAL="50"

# Function to get the id of an available GPU
get_available_gpu() {
  local mem_threshold=2000
  nvidia-smi --query-gpu=index,memory.used --format=csv,noheader,nounits | \
    awk -v threshold="$mem_threshold" -F', ' '
      $2 < threshold { print $1; exit }
    '
}



run_script(){
  while ss -tuln | grep -q ":$PORT";do
    echo "Port $PORT is in use."
    PORT=$((PORT + 1))
    echo "New port number is $PORT"
  done

  local DATASET_DIR=$1
  local DATASET_NAME=$(basename "$DATASET_DIR")
  local SPA_RATIO1=$2
  local SPA_RATIO2=$3
  local sharpness_threshold=$4
  local SPA_START_ITER=25200
  local SPA_STOP_ITER=35200
  local SPA_SG_START_ITER=25200
  local SPA_SG_STOP_ITER=35200
  local iteration=40000
  OUTPUT_DIR="./simul_v4_energy_di/"$DATASET_NAME"/3_15000for"$SPA_RATIO1"_"$SPA_START_ITER"-"$SPA_STOP_ITER"for"$SPA_RATIO2"_"$sharpness_threshold"_"$iteration""
  OUTPUT_PLY_DIR=""$OUTPUT_DIR"/point_cloud/iteration_"$iteration"/point_cloud.ply"
  echo "Output script for $OUTPUT_DIR"
  mkdir -p "$OUTPUT_DIR"
  ckpt="$OUTPUT_DIR"/chkpnt"$chkpnt_iter".pth
  SPA_RATIO=0.72
  if [ -f "$OUTPUT_DIR/results.json" ]; then
      echo "Results for SPA_RATIO1=$SPA_RATIO1, SPA_RATIO2=$SPA_RATIO2, sharpness_threshold=$sharpness_threshold already exist. Skipping this iteration."
      return # Use return instead of continue in a function
  fi

  gpu_id=$(get_available_gpu)
  if [[ -n $gpu_id ]]; then
    echo "GPU $gpu_id is available."
    CUDA_VISIBLE_DEVICES=$gpu_id python "$PYTHON_SCRIPT" \
    --port "$PORT" \
    -s="$DATASET_DIR" \
    -m="$OUTPUT_DIR" \
    --eval \
    --prune_ratio1 "$SPA_RATIO1"\
    --prune_ratio2 "$SPA_RATIO2"\
    --iterations "$iteration" \
    --optimizing_spa_start_iter "$SPA_START_ITER" \
    --optimizing_spa_stop_iter "$SPA_STOP_ITER" \
    --optimizing_spa_sg_start_iter "$SPA_SG_START_ITER" \
    --optimizing_spa_sg_stop_iter "$SPA_SG_STOP_ITER" \
    --sharpness_threshold "$sharpness_threshold"\
    --imp_metric "outdoor" 
    # -i images_4
    # --checkpoint_iterations 50000 \
    # --imp_metric "outdoor"
    #--start_checkpoint "$ckpt"

    CUDA_VISIBLE_DEVICES=$gpu_id python "$PYTHON_SCRIPT_RENDER" \
    -m="$OUTPUT_DIR" --skip_train 

    METRICS_OUTPUT=$(CUDA_VISIBLE_DEVICES=$gpu_id python "$PYTHON_SCRIPT_METRICS" \
    -m="$OUTPUT_DIR" 2>&1)
    
    echo "$METRICS_OUTPUT"

    # Extract metrics
    current_ssim=$(echo "$METRICS_OUTPUT" | grep "SSIM" | awk '{print $4}')
    current_psnr=$(echo "$METRICS_OUTPUT" | grep "PSNR" | awk '{print $4}')
    current_lpips=$(echo "$METRICS_OUTPUT" | grep "LPIPS" | awk '{print $4}')

    echo "Current SSIM: $current_ssim, PSNR: $current_psnr, LPIPS: $current_lpips"

    # Compare and update best results (only based on PSNR)
    if (( $(echo "$current_psnr > $best_psnr" | bc -l) )); then
        best_psnr=$current_psnr
        best_ssim=$current_ssim
        best_lpips=$current_lpips
        best_spa_ratio1=$SPA_RATIO1
        best_spa_ratio2=$SPA_RATIO2
        best_sharpness_threshold=$sharpness_threshold
    fi

    CUDA_VISIBLE_DEVICES=$gpu_id python "$PYTHON_SCRIPT_COUNT" "$OUTPUT_PLY_DIR"

    # Get memory usage information
    MEM_OUTPUT=$(CUDA_VISIBLE_DEVICES=$gpu_id python "$PYTHON_SCRIPT_MEM" \
    -m="$OUTPUT_DIR" 2>&1)

    echo "$MEM_OUTPUT"

    # Extract memory information
    loading_memory=$(echo "$MEM_OUTPUT" | grep "Loading peak memory:" | awk '{print $4}')
    rendering_memory=$(echo "$MEM_OUTPUT" | grep "显存平均消耗：" | awk '{print $2}')

    # If memory values are empty, set default values
    if [ -z "$loading_memory" ]; then
        loading_memory="N/A"
    fi
    if [ -z "$rendering_memory" ]; then
        rendering_memory="N/A"
    fi

    # Log results to txt file
    echo "$(date '+%Y-%m-%d %H:%M:%S') | Dataset: $DATASET_NAME | SPA_RATIO1: $SPA_RATIO1 | SPA_RATIO2: $SPA_RATIO2 | PSNR: $current_psnr | SSIM: $current_ssim | LPIPS: $current_lpips | Loading Memory: ${loading_memory}MB | Rendering Memory: ${rendering_memory}MB" >> "$RESULTS_LOG"
    else
      echo "No GPU available at the moment. Retrying in 1 minute."
      sleep 60
  fi
}

# Define parameter ranges
spa_ratio1_values=(0.4 0.45 0.5)
spa_ratio2_values=(0.7 0.72 0.75 0.8)
sharpness_threshold_values=(1.2 1.3 1.4)

# Initialize best results variables
best_psnr=-1
best_ssim=-1
best_lpips=999
best_spa_ratio1=""
best_spa_ratio2=""
best_sharpness_threshold=""

# Create or initialize results log file
echo "Training Results Log - Started at $(date '+%Y-%m-%d %H:%M:%S')" > "$RESULTS_LOG"
echo "Format: Timestamp | Dataset | SPA_RATIO1 | SPA_RATIO2 | PSNR | SSIM | LPIPS | Loading Memory | Rendering Memory" >> "$RESULTS_LOG"
echo "==================================================================================================================================================" >> "$RESULTS_LOG"

for view in "${run_scenes[@]}"; do
    echo "Running script for $view"
    DATASET_PATH="$BASE_DATASET_DIR/$view"
    for sr1 in "${spa_ratio1_values[@]}"; do
        for sr2 in "${spa_ratio2_values[@]}"; do
            for st in "${sharpness_threshold_values[@]}"; do
                echo "Running with SPA_RATIO1=$sr1, SPA_RATIO2=$sr2, sharpness_threshold=$st"
                run_script "$DATASET_PATH" "$sr1" "$sr2" "$st" 
            done
        done
    done
done

# Add summary to log file
echo "" >> "$RESULTS_LOG"
echo "==================================================================================================================================================" >> "$RESULTS_LOG"
echo "SUMMARY - Completed at $(date '+%Y-%m-%d %H:%M:%S')" >> "$RESULTS_LOG"
echo "Best results found:" >> "$RESULTS_LOG"
echo "Best PSNR: $best_psnr" >> "$RESULTS_LOG"
echo "Best SSIM: $best_ssim" >> "$RESULTS_LOG"
echo "Best LPIPS: $best_lpips" >> "$RESULTS_LOG"
echo "Corresponding parameters: SPA_RATIO1=$best_spa_ratio1, SPA_RATIO2=$best_spa_ratio2, sharpness_threshold=$best_sharpness_threshold" >> "$RESULTS_LOG"

echo "Best results found:"
echo "Best PSNR: $best_psnr"
echo "Best SSIM: $best_ssim"
echo "Best LPIPS: $best_lpips"
echo "Corresponding parameters: SPA_RATIO1=$best_spa_ratio1, SPA_RATIO2=$best_spa_ratio2, sharpness_threshold=$best_sharpness_threshold"
echo ""
echo "All training results have been saved to: $RESULTS_LOG"