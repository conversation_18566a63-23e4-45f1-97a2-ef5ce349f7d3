基准目录: GaussianSpa_output/truck/imp_score_spa_origin_40000_0.5_0.7
当前目录: simul_v4_energy_di/truck/3_15000for0.5_25200-35200for0.72_1_40000


==================================================
SSIM 指标 - 所有 32 张图像的比较结果:
==================================================

统计信息:
  平均差异: -0.003065
  差异标准差: 0.003178
  最大差异: 0.004175
  最小差异: -0.010286
  基准平均值: 0.888174
  当前平均值: 0.885109

性能变化统计:
  改进的图像: 5 (15.6%)
  退化的图像: 27 (84.4%)
  无变化的图像: 0 (0.0%)

详细结果 (按差异排序):
排名     图像名称            基准值          当前值          差异          
-----------------------------------------------------------------
1      00000.png       0.911212     0.915387     0.004175    
2      00001.png       0.910123     0.913117     0.002994    
3      00002.png       0.872864     0.875747     0.002883    
4      00030.png       0.901145     0.902227     0.001082    
5      00005.png       0.883514     0.883925     0.000411    
6      00013.png       0.913559     0.913201     -0.000359   
7      00025.png       0.901773     0.901153     -0.000620   
8      00012.png       0.894762     0.893951     -0.000811   
9      00026.png       0.892954     0.891335     -0.001620   
10     00004.png       0.882366     0.880253     -0.002113   
11     00022.png       0.903489     0.901285     -0.002204   
12     00017.png       0.869571     0.867322     -0.002248   
13     00014.png       0.880143     0.877880     -0.002263   
14     00027.png       0.832386     0.829812     -0.002573   
15     00009.png       0.926862     0.924054     -0.002808   
16     00031.png       0.903410     0.900472     -0.002939   
17     00011.png       0.888786     0.885794     -0.002992   
18     00007.png       0.868654     0.864779     -0.003875   
19     00016.png       0.875506     0.871513     -0.003993   
20     00020.png       0.911610     0.907160     -0.004450   
21     00024.png       0.911151     0.906522     -0.004629   
22     00021.png       0.882188     0.877522     -0.004666   
23     00010.png       0.881114     0.876337     -0.004777   
24     00006.png       0.900080     0.894904     -0.005176   
25     00028.png       0.888518     0.883177     -0.005341   
26     00023.png       0.912609     0.907238     -0.005370   
27     00003.png       0.875454     0.869394     -0.006060   
28     00015.png       0.856089     0.849825     -0.006264   
29     00019.png       0.884780     0.877825     -0.006954   
30     00029.png       0.874435     0.867351     -0.007085   
31     00018.png       0.849797     0.842641     -0.007156   
32     00008.png       0.880667     0.870381     -0.010286   

==================================================
PSNR 指标 - 所有 32 张图像的比较结果:
==================================================

统计信息:
  平均差异: -0.405626
  差异标准差: 0.265558
  最大差异: 0.015188
  最小差异: -1.110151
  基准平均值: 25.660289
  当前平均值: 25.254663

性能变化统计:
  改进的图像: 1 (3.1%)
  退化的图像: 31 (96.9%)
  无变化的图像: 0 (0.0%)

详细结果 (按差异排序):
排名     图像名称            基准值          当前值          差异          
-----------------------------------------------------------------
1      00019.png       23.706923    23.722111    0.015188    
2      00014.png       24.640533    24.570963    -0.069571   
3      00013.png       26.427477    26.259602    -0.167875   
4      00001.png       26.718307    26.538761    -0.179546   
5      00003.png       26.133947    25.941687    -0.192261   
6      00007.png       24.886009    24.693497    -0.192513   
7      00002.png       25.379761    25.186718    -0.193043   
8      00023.png       27.742622    27.549189    -0.193434   
9      00017.png       24.791031    24.595158    -0.195873   
10     00022.png       27.108698    26.907434    -0.201263   
11     00004.png       26.315842    26.088413    -0.227428   
12     00012.png       25.221638    24.986576    -0.235062   
13     00011.png       25.162600    24.922686    -0.239914   
14     00030.png       25.237431    24.986277    -0.251154   
15     00005.png       26.572962    26.272909    -0.300053   
16     00026.png       25.592617    25.287426    -0.305191   
17     00018.png       26.457846    26.150230    -0.307615   
18     00027.png       22.105665    21.719902    -0.385763   
19     00021.png       25.196913    24.800777    -0.396135   
20     00028.png       24.740236    24.331829    -0.408407   
21     00000.png       26.409456    26.000118    -0.409338   
22     00025.png       25.803852    25.297176    -0.506676   
23     00010.png       25.621799    25.099945    -0.521854   
24     00016.png       25.743711    25.158274    -0.585438   
25     00031.png       26.009056    25.387671    -0.621386   
26     00020.png       27.346666    26.696520    -0.650146   
27     00006.png       25.993359    25.303469    -0.689890   
28     00009.png       27.125654    26.408543    -0.717112   
29     00029.png       24.703396    23.931787    -0.771608   
30     00008.png       24.315693    23.528168    -0.787525   
31     00015.png       24.012270    23.030266    -0.982004   
32     00024.png       27.905275    26.795124    -1.110151   

==================================================
LPIPS 指标 - 所有 32 张图像的比较结果:
==================================================

统计信息:
  平均差异: -0.000517
  差异标准差: 0.006006
  最大差异: 0.016274
  最小差异: -0.012406
  基准平均值: 0.124083
  当前平均值: 0.124600

性能变化统计:
  改进的图像: 13 (40.6%)
  退化的图像: 19 (59.4%)
  无变化的图像: 0 (0.0%)

详细结果 (按差异排序):
排名     图像名称            基准值          当前值          差异          
-----------------------------------------------------------------
1      00002.png       0.120329     0.104054     0.016274    
2      00000.png       0.115188     0.103816     0.011372    
3      00001.png       0.113116     0.104108     0.009009    
4      00005.png       0.110763     0.104645     0.006117    
5      00030.png       0.107868     0.102581     0.005287    
6      00017.png       0.123258     0.118397     0.004861    
7      00027.png       0.158254     0.155572     0.002682    
8      00031.png       0.137003     0.135057     0.001945    
9      00012.png       0.108017     0.106203     0.001814    
10     00016.png       0.142081     0.140736     0.001345    
11     00025.png       0.117990     0.116694     0.001297    
12     00010.png       0.154066     0.153699     0.000368    
13     00013.png       0.108645     0.108495     0.000150    
14     00009.png       0.107524     0.107596     -0.000072   
15     00014.png       0.128863     0.128941     -0.000078   
16     00018.png       0.126207     0.126851     -0.000644   
17     00022.png       0.118312     0.118969     -0.000657   
18     00020.png       0.136721     0.137651     -0.000931   
19     00026.png       0.103598     0.104997     -0.001399   
20     00011.png       0.110813     0.112698     -0.001885   
21     00029.png       0.149695     0.151776     -0.002081   
22     00024.png       0.156019     0.159319     -0.003299   
23     00004.png       0.109259     0.112833     -0.003574   
24     00028.png       0.115939     0.120032     -0.004093   
25     00021.png       0.119457     0.123625     -0.004168   
26     00007.png       0.110111     0.114929     -0.004818   
27     00003.png       0.120217     0.125454     -0.005238   
28     00008.png       0.124017     0.129350     -0.005333   
29     00006.png       0.099605     0.106692     -0.007087   
30     00015.png       0.139210     0.149126     -0.009917   
31     00023.png       0.134174     0.145563     -0.011388   
32     00019.png       0.144349     0.156755     -0.012406   
