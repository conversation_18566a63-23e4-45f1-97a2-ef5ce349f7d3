基准目录: GaussianSpa_output/kitchen/imp_score_opa_origin
当前目录: simul_v4_energy_di/kitchen/3_15000for0.4_25200-35200for0.75_1_40000


==================================================
SSIM 指标 - 所有 35 张图像的比较结果:
==================================================

统计信息:
  平均差异: -0.004311
  差异标准差: 0.005293
  最大差异: 0.001459
  最小差异: -0.031413
  基准平均值: 0.928999
  当前平均值: 0.924688

性能变化统计:
  改进的图像: 3 (8.6%)
  退化的图像: 32 (91.4%)
  无变化的图像: 0 (0.0%)

详细结果 (按差异排序):
排名     图像名称            基准值          当前值          差异          
-----------------------------------------------------------------
1      00027.png       0.766701     0.768160     0.001459    
2      00023.png       0.939674     0.940109     0.000435    
3      00030.png       0.937922     0.938162     0.000240    
4      00006.png       0.805894     0.804392     -0.001502   
5      00033.png       0.923542     0.921962     -0.001581   
6      00017.png       0.940163     0.938552     -0.001612   
7      00008.png       0.914279     0.912647     -0.001632   
8      00011.png       0.915005     0.913312     -0.001694   
9      00016.png       0.949967     0.947918     -0.002049   
10     00031.png       0.942394     0.940226     -0.002168   
11     00002.png       0.950121     0.947760     -0.002361   
12     00013.png       0.953913     0.951338     -0.002574   
13     00012.png       0.947336     0.944662     -0.002674   
14     00005.png       0.941269     0.938550     -0.002719   
15     00026.png       0.953817     0.951035     -0.002781   
16     00003.png       0.958105     0.955311     -0.002794   
17     00032.png       0.961558     0.958739     -0.002819   
18     00009.png       0.941439     0.938290     -0.003149   
19     00001.png       0.940389     0.937185     -0.003204   
20     00034.png       0.769150     0.765491     -0.003659   
21     00028.png       0.952090     0.947978     -0.004113   
22     00007.png       0.950425     0.946286     -0.004139   
23     00025.png       0.946164     0.941637     -0.004527   
24     00020.png       0.945865     0.941324     -0.004541   
25     00010.png       0.953131     0.948543     -0.004587   
26     00000.png       0.923009     0.918250     -0.004760   
27     00004.png       0.954040     0.949020     -0.005020   
28     00022.png       0.950392     0.945233     -0.005159   
29     00019.png       0.943286     0.938032     -0.005253   
30     00029.png       0.925786     0.920494     -0.005293   
31     00024.png       0.946126     0.939796     -0.006330   
32     00015.png       0.952699     0.945985     -0.006715   
33     00014.png       0.942859     0.935988     -0.006871   
34     00018.png       0.930640     0.917320     -0.013320   
35     00021.png       0.945824     0.914411     -0.031413   

==================================================
PSNR 指标 - 所有 35 张图像的比较结果:
==================================================

统计信息:
  平均差异: -0.295925
  差异标准差: 1.084391
  最大差异: 0.975124
  最小差异: -6.304752
  基准平均值: 31.502559
  当前平均值: 31.206634

性能变化统计:
  改进的图像: 7 (20.0%)
  退化的图像: 28 (80.0%)
  无变化的图像: 0 (0.0%)

详细结果 (按差异排序):
排名     图像名称            基准值          当前值          差异          
-----------------------------------------------------------------
1      00020.png       31.722851    32.697975    0.975124    
2      00023.png       31.445938    32.244247    0.798309    
3      00030.png       31.941437    32.357536    0.416100    
4      00014.png       30.874615    31.039200    0.164585    
5      00016.png       33.402813    33.485912    0.083099    
6      00019.png       32.194756    32.216846    0.022091    
7      00006.png       27.960289    27.970667    0.010378    
8      00031.png       31.732422    31.723558    -0.008863   
9      00028.png       32.282516    32.273094    -0.009422   
10     00012.png       31.319407    31.305614    -0.013792   
11     00027.png       27.299803    27.270313    -0.029490   
12     00011.png       31.661430    31.629211    -0.032219   
13     00005.png       30.176849    30.112774    -0.064075   
14     00001.png       29.077347    29.005989    -0.071358   
15     00017.png       33.638988    33.537304    -0.101685   
16     00008.png       30.125683    30.010958    -0.114725   
17     00034.png       28.388916    28.271748    -0.117168   
18     00032.png       35.195480    35.059151    -0.136330   
19     00003.png       32.054813    31.915529    -0.139284   
20     00004.png       31.618519    31.447170    -0.171349   
21     00013.png       32.816330    32.618919    -0.197411   
22     00009.png       32.144753    31.942503    -0.202250   
23     00002.png       32.185493    31.952744    -0.232750   
24     00025.png       32.178425    31.931093    -0.247332   
25     00026.png       33.734760    33.461067    -0.273693   
26     00010.png       32.532043    32.185192    -0.346851   
27     00024.png       31.768890    31.419971    -0.348919   
28     00029.png       31.839985    31.457516    -0.382469   
29     00000.png       27.769054    27.366112    -0.402943   
30     00022.png       33.903183    33.491863    -0.411320   
31     00033.png       30.559521    30.061247    -0.498274   
32     00018.png       31.547602    31.023901    -0.523701   
33     00015.png       32.414314    31.735294    -0.679020   
34     00007.png       29.793594    29.027987    -0.765608   
35     00021.png       33.286743    26.981991    -6.304752   

==================================================
LPIPS 指标 - 所有 35 张图像的比较结果:
==================================================

统计信息:
  平均差异: -0.004153
  差异标准差: 0.005325
  最大差异: 0.003397
  最小差异: -0.019515
  基准平均值: 0.126291
  当前平均值: 0.130445

性能变化统计:
  改进的图像: 7 (20.0%)
  退化的图像: 28 (80.0%)
  无变化的图像: 0 (0.0%)

详细结果 (按差异排序):
排名     图像名称            基准值          当前值          差异          
-----------------------------------------------------------------
1      00027.png       0.272259     0.268861     0.003397    
2      00033.png       0.171178     0.167884     0.003294    
3      00031.png       0.150912     0.148968     0.001945    
4      00023.png       0.134565     0.132989     0.001575    
5      00008.png       0.165555     0.164145     0.001409    
6      00017.png       0.121890     0.120536     0.001354    
7      00012.png       0.101779     0.101489     0.000290    
8      00002.png       0.104215     0.104351     -0.000136   
9      00013.png       0.131433     0.132137     -0.000704   
10     00003.png       0.083530     0.084626     -0.001096   
11     00034.png       0.286822     0.288250     -0.001428   
12     00030.png       0.120817     0.122484     -0.001668   
13     00016.png       0.095729     0.097469     -0.001739   
14     00026.png       0.099314     0.101055     -0.001741   
15     00001.png       0.107635     0.109530     -0.001895   
16     00032.png       0.125769     0.128520     -0.002751   
17     00009.png       0.124396     0.127507     -0.003111   
18     00022.png       0.103023     0.106804     -0.003780   
19     00019.png       0.111837     0.116095     -0.004258   
20     00025.png       0.088262     0.092747     -0.004484   
21     00029.png       0.126687     0.131276     -0.004590   
22     00011.png       0.138075     0.142759     -0.004684   
23     00028.png       0.071479     0.076185     -0.004706   
24     00010.png       0.122925     0.128008     -0.005083   
25     00007.png       0.081700     0.086910     -0.005210   
26     00024.png       0.094959     0.100200     -0.005240   
27     00020.png       0.096111     0.102237     -0.006126   
28     00005.png       0.107204     0.113363     -0.006159   
29     00004.png       0.087443     0.094815     -0.007372   
30     00006.png       0.225625     0.234371     -0.008746   
31     00000.png       0.127444     0.136468     -0.009024   
32     00014.png       0.104424     0.114560     -0.010135   
33     00015.png       0.112574     0.127161     -0.014588   
34     00018.png       0.109786     0.128437     -0.018651   
35     00021.png       0.112845     0.132360     -0.019515   
