基准目录: GaussiansSpa_output/room/imp_score_spa_origin_40000_0.5_0.7
当前目录: simul_v4_energy_di/room/3_15000for0.6_25200-35200for0.75_0.4_40000


==================================================
SSIM 指标 - 所有 39 张图像的比较结果:
==================================================

统计信息:
  平均差异: -0.002728
  差异标准差: 0.003039
  最大差异: 0.002836
  最小差异: -0.012020
  基准平均值: 0.922767
  当前平均值: 0.920039

性能变化统计:
  改进的图像: 7 (17.9%)
  退化的图像: 32 (82.1%)
  无变化的图像: 0 (0.0%)

详细结果 (按差异排序):
排名     图像名称            基准值          当前值          差异          
-----------------------------------------------------------------
1      00037.png       0.927545     0.930381     0.002836    
2      00038.png       0.895279     0.897584     0.002304    
3      00021.png       0.867167     0.869016     0.001848    
4      00018.png       0.916339     0.917673     0.001334    
5      00036.png       0.865240     0.866122     0.000883    
6      00015.png       0.895214     0.895685     0.000472    
7      00032.png       0.920970     0.920997     0.000027    
8      00004.png       0.945401     0.945155     -0.000246   
9      00027.png       0.916388     0.916015     -0.000373   
10     00030.png       0.890592     0.889462     -0.001130   
11     00019.png       0.935152     0.934016     -0.001136   
12     00014.png       0.929870     0.928460     -0.001411   
13     00035.png       0.944742     0.943331     -0.001411   
14     00009.png       0.946881     0.945341     -0.001541   
15     00007.png       0.953418     0.951636     -0.001782   
16     00016.png       0.920194     0.918383     -0.001811   
17     00033.png       0.928291     0.926315     -0.001975   
18     00008.png       0.931389     0.929196     -0.002193   
19     00023.png       0.904305     0.902084     -0.002221   
20     00011.png       0.951069     0.948695     -0.002374   
21     00029.png       0.924728     0.922292     -0.002436   
22     00028.png       0.920172     0.917530     -0.002641   
23     00017.png       0.947879     0.945226     -0.002653   
24     00025.png       0.926356     0.923588     -0.002768   
25     00026.png       0.932965     0.929749     -0.003217   
26     00012.png       0.946580     0.943251     -0.003329   
27     00003.png       0.938589     0.935037     -0.003552   
28     00024.png       0.928589     0.924834     -0.003755   
29     00001.png       0.912830     0.908191     -0.004638   
30     00010.png       0.940960     0.936146     -0.004814   
31     00020.png       0.939140     0.934193     -0.004947   
32     00005.png       0.938758     0.933781     -0.004977   
33     00031.png       0.874650     0.869617     -0.005032   
34     00013.png       0.921526     0.914920     -0.006606   
35     00022.png       0.941672     0.935034     -0.006639   
36     00000.png       0.894394     0.887213     -0.007181   
37     00006.png       0.926815     0.919571     -0.007244   
38     00034.png       0.936585     0.928537     -0.008048   
39     00002.png       0.909292     0.897273     -0.012020   

==================================================
PSNR 指标 - 所有 39 张图像的比较结果:
==================================================

统计信息:
  平均差异: -0.191941
  差异标准差: 0.258638
  最大差异: 0.455372
  最小差异: -0.777039
  基准平均值: 31.618210
  当前平均值: 31.426269

性能变化统计:
  改进的图像: 6 (15.4%)
  退化的图像: 33 (84.6%)
  无变化的图像: 0 (0.0%)

详细结果 (按差异排序):
排名     图像名称            基准值          当前值          差异          
-----------------------------------------------------------------
1      00031.png       31.871250    32.326622    0.455372    
2      00018.png       32.556538    32.897095    0.340557    
3      00029.png       29.753895    29.940430    0.186535    
4      00004.png       33.256168    33.394417    0.138248    
5      00016.png       32.364086    32.462898    0.098812    
6      00038.png       31.388680    31.397373    0.008694    
7      00037.png       32.213997    32.212074    -0.001923   
8      00035.png       29.799921    29.796503    -0.003418   
9      00027.png       31.755312    31.742352    -0.012960   
10     00023.png       27.994846    27.970013    -0.024834   
11     00005.png       30.432673    30.355751    -0.076921   
12     00014.png       28.360300    28.259521    -0.100779   
13     00009.png       34.227211    34.123646    -0.103565   
14     00019.png       30.094585    29.986454    -0.108131   
15     00021.png       30.207653    30.077593    -0.130060   
16     00028.png       31.266356    31.129171    -0.137184   
17     00036.png       22.142275    22.004519    -0.137756   
18     00032.png       27.858196    27.717579    -0.140617   
19     00003.png       34.998032    34.839222    -0.158810   
20     00017.png       34.733398    34.568726    -0.164673   
21     00001.png       33.525806    33.349827    -0.175980   
22     00008.png       31.358416    31.177389    -0.181026   
23     00010.png       33.230412    33.013824    -0.216587   
24     00020.png       32.071335    31.853659    -0.217676   
25     00030.png       31.281870    31.059780    -0.222090   
26     00024.png       31.549755    31.326389    -0.223366   
27     00022.png       33.788601    33.523087    -0.265514   
28     00025.png       32.784485    32.498508    -0.285976   
29     00015.png       32.660965    32.349270    -0.311695   
30     00011.png       36.527821    36.169228    -0.358593   
31     00013.png       32.258217    31.821642    -0.436575   
32     00026.png       31.515247    31.058237    -0.457010   
33     00002.png       33.598675    33.129372    -0.469303   
34     00000.png       30.574072    30.102543    -0.471529   
35     00012.png       35.566845    35.084015    -0.482830   
36     00007.png       33.436970    32.917152    -0.519817   
37     00033.png       31.180855    30.584051    -0.596804   
38     00034.png       25.814137    25.071280    -0.742857   
39     00006.png       33.110332    32.333294    -0.777039   

==================================================
LPIPS 指标 - 所有 39 张图像的比较结果:
==================================================

统计信息:
  平均差异: -0.003983
  差异标准差: 0.004904
  最大差异: 0.004838
  最小差异: -0.014309
  基准平均值: 0.210559
  当前平均值: 0.214542

性能变化统计:
  改进的图像: 9 (23.1%)
  退化的图像: 30 (76.9%)
  无变化的图像: 0 (0.0%)

详细结果 (按差异排序):
排名     图像名称            基准值          当前值          差异          
-----------------------------------------------------------------
1      00027.png       0.234813     0.229975     0.004838    
2      00019.png       0.174881     0.171172     0.003709    
3      00026.png       0.213394     0.210817     0.002577    
4      00018.png       0.249342     0.247726     0.001616    
5      00015.png       0.220718     0.219344     0.001375    
6      00004.png       0.199936     0.198781     0.001155    
7      00033.png       0.208518     0.207692     0.000826    
8      00030.png       0.282995     0.282355     0.000641    
9      00035.png       0.283691     0.283097     0.000594    
10     00023.png       0.182040     0.182329     -0.000290   
11     00007.png       0.172564     0.173392     -0.000828   
12     00036.png       0.365255     0.366604     -0.001349   
13     00037.png       0.277853     0.279616     -0.001763   
14     00000.png       0.195194     0.197127     -0.001933   
15     00032.png       0.200351     0.202435     -0.002084   
16     00008.png       0.186758     0.189170     -0.002412   
17     00029.png       0.234523     0.237596     -0.003073   
18     00021.png       0.305004     0.308191     -0.003187   
19     00038.png       0.289768     0.293183     -0.003414   
20     00009.png       0.164980     0.168412     -0.003432   
21     00011.png       0.158593     0.162165     -0.003572   
22     00031.png       0.259457     0.263321     -0.003864   
23     00001.png       0.162680     0.166631     -0.003951   
24     00014.png       0.178727     0.182755     -0.004028   
25     00017.png       0.159208     0.163581     -0.004372   
26     00016.png       0.211101     0.216171     -0.005070   
27     00025.png       0.217703     0.223360     -0.005657   
28     00012.png       0.160298     0.166167     -0.005869   
29     00005.png       0.171986     0.177887     -0.005901   
30     00003.png       0.160797     0.167019     -0.006223   
31     00028.png       0.214841     0.221374     -0.006532   
32     00013.png       0.195007     0.201556     -0.006549   
33     00024.png       0.228477     0.237893     -0.009417   
34     00010.png       0.162856     0.173194     -0.010338   
35     00022.png       0.120484     0.132424     -0.011939   
36     00020.png       0.150748     0.163593     -0.012845   
37     00002.png       0.176303     0.190473     -0.014170   
38     00034.png       0.299515     0.313809     -0.014294   
39     00006.png       0.180433     0.194742     -0.014309   
