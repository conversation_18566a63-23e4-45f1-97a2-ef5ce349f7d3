#!/usr/bin/env python3
"""
Test script to verify the memory test script outputs the correct format
"""

import subprocess
import sys
import os

def test_memory_script():
    """Test if the memory script outputs the expected format"""
    
    # Check if the memory script exists
    script_path = "./render_spherical_gaussian_mem_test_v2_light.py"
    if not os.path.exists(script_path):
        print(f"Error: {script_path} not found")
        return False
    
    print("Memory script found. Testing output format...")
    
    # Create a simple test to see if the script can be imported
    try:
        import torch
        from scene import Scene
        from scene.spherical_gaussian_model_cullSG_v5 import SphericalGaussianModelcullSGv5
        from spherical_gaussian_renderer_v2_light import render, render_imp
        print("All required modules can be imported successfully")
        return True
    except ImportError as e:
        print(f"Import error: {e}")
        return False

if __name__ == "__main__":
    success = test_memory_script()
    if success:
        print("Memory script test passed!")
        sys.exit(0)
    else:
        print("Memory script test failed!")
        sys.exit(1)
