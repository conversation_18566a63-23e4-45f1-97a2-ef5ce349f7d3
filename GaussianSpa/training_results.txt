Training Results Log - Started at 2025-08-16 16:14:22
Format: Timestamp | Dataset | SPA_RATIO1 | SPA_RATIO2 | PSNR | SSIM | LPIPS | Loading Memory | Rendering Memory
==================================================================================================================================================
2025-08-16 16:49:03 | Dataset: train | SPA_RATIO1: 0.4 | SPA_RATIO2: 0.65 | PSNR:  | SSIM:  | LPIPS:  | Loading Memory: 66.052734375
66.052734375MB | Rendering Memory: [16/08MB
2025-08-16 17:23:22 | Dataset: train | SPA_RATIO1: 0.4 | SPA_RATIO2: 0.68 | PSNR:  | SSIM:  | LPIPS:  | Loading Memory: 65.43017578125
65.43017578125MB | Rendering Memory: [16/08MB
2025-08-16 17:56:57 | Dataset: train | SPA_RATIO1: 0.4 | SPA_RATIO2: 0.7 | PSNR:  | SSIM:  | LPIPS:  | Loading Memory: 54.81396484375
54.81396484375MB | Rendering Memory: [16/08MB
2025-08-16 18:30:28 | Dataset: train | SPA_RATIO1: 0.4 | SPA_RATIO2: 0.72 | PSNR:  | SSIM:  | LPIPS:  | Loading Memory: 51.6162109375
51.6162109375MB | Rendering Memory: [16/08MB
2025-08-16 19:04:29 | Dataset: train | SPA_RATIO1: 0.4 | SPA_RATIO2: 0.78 | PSNR:  | SSIM:  | LPIPS:  | Loading Memory: 42.24755859375
42.24755859375MB | Rendering Memory: [16/08MB
2025-08-16 19:38:48 | Dataset: train | SPA_RATIO1: 0.4 | SPA_RATIO2: 0.8 | PSNR:  | SSIM:  | LPIPS:  | Loading Memory: 37.904296875
37.904296875MB | Rendering Memory: [16/08MB
2025-08-16 20:11:00 | Dataset: train | SPA_RATIO1: 0.45 | SPA_RATIO2: 0.65 | PSNR:  | SSIM:  | LPIPS:  | Loading Memory: 62.93359375
62.93359375MB | Rendering Memory: [16/08MB
2025-08-16 20:43:07 | Dataset: train | SPA_RATIO1: 0.45 | SPA_RATIO2: 0.68 | PSNR:  | SSIM:  | LPIPS:  | Loading Memory: 59.9892578125
59.9892578125MB | Rendering Memory: [16/08MB
2025-08-16 21:21:02 | Dataset: train | SPA_RATIO1: 0.45 | SPA_RATIO2: 0.72 | PSNR:  | SSIM:  | LPIPS:  | Loading Memory: 48.7216796875
48.7216796875MB | Rendering Memory: [16/08MB
2025-08-16 21:53:19 | Dataset: train | SPA_RATIO1: 0.45 | SPA_RATIO2: 0.75 | PSNR:  | SSIM:  | LPIPS:  | Loading Memory: 43.2744140625
43.2744140625MB | Rendering Memory: [16/08MB
2025-08-16 22:26:00 | Dataset: train | SPA_RATIO1: 0.45 | SPA_RATIO2: 0.78 | PSNR:  | SSIM:  | LPIPS:  | Loading Memory: 38.03955078125
38.03955078125MB | Rendering Memory: [16/08MB
2025-08-16 22:59:01 | Dataset: train | SPA_RATIO1: 0.45 | SPA_RATIO2: 0.8 | PSNR:  | SSIM:  | LPIPS:  | Loading Memory: 35.296875
35.296875MB | Rendering Memory: [16/08MB
2025-08-16 23:29:52 | Dataset: train | SPA_RATIO1: 0.5 | SPA_RATIO2: 0.65 | PSNR:  | SSIM:  | LPIPS:  | Loading Memory: 59.42529296875
59.42529296875MB | Rendering Memory: [16/08MB
2025-08-17 00:00:32 | Dataset: train | SPA_RATIO1: 0.5 | SPA_RATIO2: 0.68 | PSNR:  | SSIM:  | LPIPS:  | Loading Memory: 50.0322265625
50.0322265625MB | Rendering Memory: [17/08MB
2025-08-17 00:31:30 | Dataset: train | SPA_RATIO1: 0.5 | SPA_RATIO2: 0.72 | PSNR:  | SSIM:  | LPIPS:  | Loading Memory: 44.65771484375
44.65771484375MB | Rendering Memory: [17/08MB
2025-08-17 01:02:41 | Dataset: train | SPA_RATIO1: 0.5 | SPA_RATIO2: 0.75 | PSNR:  | SSIM:  | LPIPS:  | Loading Memory: 39.76513671875
39.76513671875MB | Rendering Memory: [17/08MB
2025-08-17 01:34:20 | Dataset: train | SPA_RATIO1: 0.5 | SPA_RATIO2: 0.78 | PSNR:  | SSIM:  | LPIPS:  | Loading Memory: 34.9921875
34.9921875MB | Rendering Memory: [17/08MB
2025-08-17 02:06:22 | Dataset: train | SPA_RATIO1: 0.5 | SPA_RATIO2: 0.8 | PSNR:  | SSIM:  | LPIPS:  | Loading Memory: 31.75341796875
31.75341796875MB | Rendering Memory: [17/08MB
2025-08-17 02:36:04 | Dataset: train | SPA_RATIO1: 0.55 | SPA_RATIO2: 0.65 | PSNR:  | SSIM:  | LPIPS:  | Loading Memory: 55.77490234375
55.77490234375MB | Rendering Memory: [17/08MB
2025-08-17 03:05:28 | Dataset: train | SPA_RATIO1: 0.55 | SPA_RATIO2: 0.68 | PSNR:  | SSIM:  | LPIPS:  | Loading Memory: 45.76318359375
45.76318359375MB | Rendering Memory: [17/08MB
2025-08-17 03:35:14 | Dataset: train | SPA_RATIO1: 0.55 | SPA_RATIO2: 0.7 | PSNR:  | SSIM:  | LPIPS:  | Loading Memory: 44.49560546875
44.49560546875MB | Rendering Memory: [17/08MB
2025-08-17 04:04:51 | Dataset: train | SPA_RATIO1: 0.55 | SPA_RATIO2: 0.72 | PSNR:  | SSIM:  | LPIPS:  | Loading Memory: 40.6748046875
40.6748046875MB | Rendering Memory: [17/08MB
2025-08-17 04:34:56 | Dataset: train | SPA_RATIO1: 0.55 | SPA_RATIO2: 0.75 | PSNR:  | SSIM:  | LPIPS:  | Loading Memory: 37.0419921875
37.0419921875MB | Rendering Memory: [17/08MB
2025-08-17 05:05:26 | Dataset: train | SPA_RATIO1: 0.55 | SPA_RATIO2: 0.78 | PSNR:  | SSIM:  | LPIPS:  | Loading Memory: 31.8544921875
31.8544921875MB | Rendering Memory: [17/08MB
2025-08-17 05:36:14 | Dataset: train | SPA_RATIO1: 0.55 | SPA_RATIO2: 0.8 | PSNR:  | SSIM:  | LPIPS:  | Loading Memory: 28.81298828125
28.81298828125MB | Rendering Memory: [17/08MB
2025-08-17 06:04:12 | Dataset: train | SPA_RATIO1: 0.6 | SPA_RATIO2: 0.65 | PSNR:  | SSIM:  | LPIPS:  | Loading Memory: 44.9716796875
44.9716796875MB | Rendering Memory: [17/08MB
2025-08-17 06:32:19 | Dataset: train | SPA_RATIO1: 0.6 | SPA_RATIO2: 0.68 | PSNR:  | SSIM:  | LPIPS:  | Loading Memory: 42.59619140625
42.59619140625MB | Rendering Memory: [17/08MB
2025-08-17 07:00:38 | Dataset: train | SPA_RATIO1: 0.6 | SPA_RATIO2: 0.7 | PSNR:  | SSIM:  | LPIPS:  | Loading Memory: 39.689453125
39.689453125MB | Rendering Memory: [17/08MB
2025-08-17 07:29:00 | Dataset: train | SPA_RATIO1: 0.6 | SPA_RATIO2: 0.72 | PSNR:  | SSIM:  | LPIPS:  | Loading Memory: 36.97900390625
36.97900390625MB | Rendering Memory: [17/08MB
2025-08-17 07:57:43 | Dataset: train | SPA_RATIO1: 0.6 | SPA_RATIO2: 0.75 | PSNR:  | SSIM:  | LPIPS:  | Loading Memory: 32.80078125
32.80078125MB | Rendering Memory: [17/08MB
2025-08-17 08:26:50 | Dataset: train | SPA_RATIO1: 0.6 | SPA_RATIO2: 0.78 | PSNR:  | SSIM:  | LPIPS:  | Loading Memory: 28.71240234375
28.71240234375MB | Rendering Memory: [17/08MB
2025-08-17 08:56:22 | Dataset: train | SPA_RATIO1: 0.6 | SPA_RATIO2: 0.8 | PSNR:  | SSIM:  | LPIPS:  | Loading Memory: 26.0302734375
26.0302734375MB | Rendering Memory: [17/08MB

==================================================================================================================================================
SUMMARY - Completed at 2025-08-17 08:56:22
Best results found:
Best PSNR: -1
Best SSIM: -1
Best LPIPS: 999
Corresponding parameters: SPA_RATIO1=, SPA_RATIO2=, sharpness_threshold=1
